#ifndef StateButton_h
#define StateButton_h

#include <Arduino.h>

enum EVENTTYPE{
    NOTHING,
    PRESS,
    HOLD,
    HOLDRETRIG
};

// TODO: Create a better system for tracking hold presses, holds and hold retriggers

// Class to track the events of the button
class PressEvent{
    EVENTTYPE type;
    size_t count;
    // size_t retriggers;
    unsigned long time;

    public:
        PressEvent(EVENTTYPE t = NOTHING, int a = 0, unsigned long time = 0 , int retriggers = 0);

        EVENTTYPE gettype() const;
        int getcount() const;
        unsigned long eventtime() const;
        void set(EVENTTYPE t, int a);
        void reset();
        bool istype(EVENTTYPE t) const;
        bool isPressed(int c) const;
        bool isHeld(int c) const;
        bool isHoldRetrig(int c) const;
        bool isNothing() const;
};


enum BUTTONSTATE{
        IDLE, // No events
        RELEASED, // Inactive state after press
        PRESSED, // Active state after press
        HELD // Held state after press
};


struct ButtonState{
    BUTTONSTATE val;
    unsigned long time;
    int count;

    ButtonState(); // Default constructor
    void set(BUTTONSTATE v, int c);
    void inc();
    void settime();
    void settime(unsigned long t);
    void reset();
    BUTTONSTATE get() const;
};


class StateButton{

    struct RawState{
        int val;
        unsigned long time;
        RawState();
        void set(int s, unsigned long t);
    };

    struct SteadyState{
        int val;
        unsigned long time;
        SteadyState();
        void set(int v, unsigned long t);
    };

    RawState rawstate;
    SteadyState steadystate;
    // Config parameters
    int pin;

    unsigned long steadyDelay;  // Jitter delay
    int activeState;            // Active state of the button, HIGH or LOW
    unsigned long pressWindow;  // Time window to check for another press
    unsigned long holdWindow;   // Time window for which the button
                                // is considered held
    int maxPresses;             // Maximum number of consecutive presses
                                // that can be registered
    unsigned long nextRetriggerTime; // Time to retrigger the hold state

    protected:
        ButtonState state;
        PressEvent pevent;

        StateButton(int pin, int maxPresses = 2,
                    unsigned long steadyDelay = 7,
                    int activeState = LOW,
                    unsigned long pressWindow = 225,
                    unsigned long holdWindow = 300);

        virtual ~StateButton();

        bool steadyRead(); // Returns true if the steady state has changed
        bool isActive() const;
        bool isInactive() const;

        virtual void monitorPress() = 0;

        // State transition functions
        void idle_Active();
        void released_Active();
        void released_Inactive();
        void pressed_Active();
        void pressed_Inactive();
        void held_Active();
        void held_Inactive();

    public:
        void pullUpMode();
        int getState() const;

        // Setters
        void setSteadyDelay(const unsigned long newdelay);
        void setPressWindow(const unsigned long newwindow);
        void setHoldWindow(const unsigned long newwindow);
        void setMaxPresses(const int newmax);
        void setActiveState(const int newstate);
        void setNextRetriggerTime(unsigned long t);
        void noRetrig();

        // Check event
        bool noevent() const;
        bool isPressed() const;
        bool isHoldEvent() const;
        bool isPressed(int c) const;
        bool isHoldEvent(int c) const;
        bool isHeld(int c = 1);
        bool isHoldEventRetrig(unsigned long t, int c = 1);

        // Debug functions
        void showEvent() const;
        void debugSteadyState();
        void debugRawState();
        unsigned long getHoldDuration() const;
};

class PressHoldButton: public StateButton{
    public:
        PressHoldButton(int pin, int maxPresses = 2,
                       unsigned long steadyDelay = 7,
                       int activeState = LOW,
                       unsigned long pressWindow = 225,
                       unsigned long holdWindow = 300);

        void monitorPress() override;
};

#endif
