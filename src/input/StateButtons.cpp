#include "StateButtons.h"

// PressEvent implementation
PressEvent::PressEvent(EVENTTYPE t, int a, unsigned long time, int retriggers)
    : type(t), count(a), time(time) {
}

EVENTTYPE PressEvent::gettype() const {
    return type;
}

int PressEvent::getcount() const {
    return count;
}

unsigned long PressEvent::eventtime() const {
    return time;
}

void PressEvent::set(EVENTTYPE t, int a) {
    type = t;
    count = a;
    time = millis();
}

void PressEvent::reset() {
    type = NOTHING;
    count = 0;
    time = 0;
}

bool PressEvent::istype(EVENTTYPE t) const {
    return type == t;
}

bool PressEvent::isPressed(int c) const {
    return istype(PRESS) && count == c;
}

bool PressEvent::isHeld(int c) const {
    return istype(HOLD) && count == c;
}

bool PressEvent::isHoldRetrig(int c) const {
    return (istype(HOLDRETRIG) || istype(HOLD)) && count == c;
}

bool PressEvent::isNothing() const {
    return istype(NOTHING);
}

// ButtonState implementation
ButtonState::ButtonState() {
    val = IDLE;
    time = 0;
    count = 0;
}

void ButtonState::set(BUTTONSTATE v, int c) {
    val = v;
    time = millis();
    count = c;
}

void ButtonState::inc() {
    count++;
}

void ButtonState::settime() {
    time = millis();
}

void ButtonState::settime(unsigned long t) {
    time = t;
}

void ButtonState::reset() {
    val = IDLE;
    count = 0;
    time = 0;
}

BUTTONSTATE ButtonState::get() const {
    return val;
}

// StateButton::RawState implementation
StateButton::RawState::RawState() {
    val = 0;
    time = 0;
}

void StateButton::RawState::set(int s, unsigned long t) {
    val = s;
    time = t;
}

// StateButton::SteadyState implementation
StateButton::SteadyState::SteadyState() {
    val = 0;
    time = 0;
}

void StateButton::SteadyState::set(int v, unsigned long t) {
    val = v;
    time = t;
}

// StateButton implementation
StateButton::StateButton(int pin, int maxPresses, unsigned long steadyDelay, 
                        int activeState, unsigned long pressWindow, unsigned long holdWindow)
    : state(ButtonState()),
      pevent(PressEvent()),
      pin(pin),
      steadyDelay(steadyDelay),
      activeState(activeState),
      pressWindow(pressWindow),
      holdWindow(holdWindow),
      maxPresses(maxPresses),
      nextRetriggerTime(0) {
    pinMode(pin, INPUT);
    rawstate.set(digitalRead(pin), 0);
    auto lowstate = activeState == HIGH ? LOW : HIGH;
    steadystate.set(lowstate, 0);
}

StateButton::~StateButton() = default;

bool StateButton::steadyRead() {
    int readstate = digitalRead(pin);
    if (readstate != rawstate.val) {     // Raw state changes
        rawstate.set(readstate, millis()); // Note time of change
    }
    else { // Raw state remained the same
        unsigned long thistime = millis();
        if (thistime - rawstate.time > steadyDelay) { // And steady delay has passed
            if (readstate != steadystate.val) { // If the steady state is different
                steadystate.set(readstate, thistime);
                return true;
            }
        }
    }
    return false;
}

bool StateButton::isActive() const {
    return steadystate.val == activeState;
}

bool StateButton::isInactive() const {
    return steadystate.val != activeState;
}

void StateButton::pullUpMode(){
    pinMode(pin, INPUT_PULLUP);
    activeState = LOW;
    rawstate.set(digitalRead(pin), 0);
    steadystate.set(LOW, 0);
}

int StateButton::getState() const {
    return state.get();
}

// Setters
void StateButton::setSteadyDelay(const unsigned long newdelay) {
    steadyDelay = newdelay;
}

void StateButton::setPressWindow(const unsigned long newwindow) {
    pressWindow = newwindow;
}

void StateButton::setHoldWindow(const unsigned long newwindow) {
    holdWindow = newwindow;
}

void StateButton::setMaxPresses(const int newmax) {
    maxPresses = newmax;
}

void StateButton::setActiveState(const int newstate) {
    activeState = newstate;
}

void StateButton::setNextRetriggerTime(unsigned long t) {
    nextRetriggerTime = t;
}

void StateButton::noRetrig() {
    nextRetriggerTime = 0;
}

// Check event
bool StateButton::noevent() const {
    return pevent.isNothing();
}

bool StateButton::isPressed() const {
    return pevent.istype(PRESS);
}

bool StateButton::isHoldEvent() const {
    return pevent.istype(HOLD);
}

bool StateButton::isPressed(int c) const {
    return pevent.isPressed(c);
}

bool StateButton::isHoldEvent(int c) const {
    return pevent.isHeld(c);
}

bool StateButton::isHeld(int c) {
    return state.get() == HELD && state.count == c;
}

bool StateButton::isHoldEventRetrig(unsigned long t, int c) {
    if (pevent.isHoldRetrig(c)) {
        auto eventtime = pevent.eventtime();
        setNextRetriggerTime(eventtime + t);
        return true;
    }
    return false;
}

void StateButton::showEvent() const {
    if (!pevent.isNothing()) {
        Serial.print("Event: ");
        Serial.println(pevent.gettype());
        Serial.print("Count: ");
        Serial.println(pevent.getcount());
    }
}

void StateButton::debugSteadyState() {
    Serial.print("Steady State: ");
    Serial.println(steadystate.val);
    Serial.print("Time: ");
    Serial.println(steadystate.time);
}

void StateButton::debugRawState() {
    Serial.print("Raw State: ");
    Serial.println(rawstate.val);
    Serial.print("Time: ");
    Serial.println(rawstate.time);
}

unsigned long StateButton::getHoldDuration() const {
    if (state.get() == HELD) {
        return millis() - state.time;
    }
    return 0;
}

// State transition functions
void StateButton::idle_Active() {
    state.set(PRESSED, 1);
}

void StateButton::released_Active() {
    state.set(PRESSED, state.count + 1);
    pevent.reset();
}

void StateButton::released_Inactive() {
    if (millis() - state.time > pressWindow) {
        pevent.set(PRESS, state.count);
        state.reset();
    }
}

void StateButton::pressed_Active() {
    if (millis() - state.time > holdWindow) {
        state.set(HELD, state.count);
        pevent.set(HOLD, state.count);
    }
}

void StateButton::pressed_Inactive() {
    if (state.count == maxPresses) {
        pevent.set(PRESS, state.count);
        state.reset();
        return;
    }
    state.set(RELEASED, state.count);
}

void StateButton::held_Active() {
    if (nextRetriggerTime == 0) {
        pevent.reset();
        return;
    }
    if (millis() > nextRetriggerTime) {
        pevent.set(HOLDRETRIG, state.count);
        nextRetriggerTime = 0;
    }
    else {
        pevent.reset();
    }
}

void StateButton::held_Inactive() {
    state.reset();
}

// PressHoldButton implementation
PressHoldButton::PressHoldButton(int pin, int maxPresses, unsigned long steadyDelay,
                                int activeState, unsigned long pressWindow, unsigned long holdWindow)
    : StateButton(pin, maxPresses, steadyDelay, activeState, pressWindow, holdWindow) {
}

void PressHoldButton::monitorPress() {
    pevent.reset(); // Reset the press event
    steadyRead();

    switch (state.get()) {
        case IDLE:
            if (isActive()) { // IDLE -> PRESSED
                idle_Active();
            }
            else if (isInactive()) { // IDLE -> IDLE
                pevent.reset();
            }
            break;
        case PRESSED:
            if (!isActive()) { // PRESSED -> RELEASED
                pressed_Inactive();
            }
            else if (isActive()) { // PRESSED -> HELD
                pressed_Active();
            }
            break;
        case RELEASED:
            if (isActive()) { // RELEASED -> PRESSED
                released_Active();
            }
            else if (isInactive()) { // RELEASED -> IDLE
                released_Inactive();
            }
            break;
        case HELD:
            if (!isActive()) { // HELD -> RELEASED
                held_Inactive();
            }
            else if (isActive()) { // HELD -> HELD
                held_Active();
            }
            break;
    }
}
