#include "fan.h"
#include <Arduino.h>
#include "../ui/vars.h"

void fan_init(void) {
    pinMode(FAN_PWM_PIN, OUTPUT);
    analogWrite(FAN_PWM_PIN, 0); // Start with fan off
}

void fan_set_speed(uint8_t speed_percent) {
    // Constrain speed to 0-100%
    if (speed_percent > 100) speed_percent = 100;
    
    // Convert percentage to PWM value (0-255)
    uint8_t pwm_value = map(speed_percent, 0, 100, 0, 255);
    
    // Set PWM output
    analogWrite(FAN_PWM_PIN, pwm_value);
}

void fan_increment_speed(int8_t increment) {
    int32_t speed = get_var_speed();
    speed += increment;
    if (speed < 0) speed = 0;
    if (speed > 100) speed = 100;
    set_var_speed(speed);
}

void fan_update(void) {
    // Get current speed from variable
    int32_t speed = get_var_speed();
    
    // Constrain to valid range
    if (speed < 0) speed = 0;
    if (speed > 100) speed = 100;
    
    // Set fan speed
    fan_set_speed((uint8_t)speed);
}