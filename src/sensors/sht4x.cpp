#include "sht4x.h"
#include <Arduino.h>
#include <Adafruit_SHT4x.h>
#include <Wire.h>
#include "../ui/vars.h"

// Create sensor instance
static Adafruit_SHT4x sht4x = Adafruit_SHT4x();

// Measurement interval (ms)
static uint32_t last_measurement_time = 0;
static const uint32_t MEASUREMENT_INTERVAL_MS = 2000; // 2 seconds

void sht4x_init(void) {
    // Initialize I2C
    Wire.begin();

    // Initialize sensor
    if (!sht4x.begin()) {
        Serial.println("Couldn't find SHT4x");
        return;
    }

    Serial.println("Found SHT4x sensor");
    Serial.print("Serial number 0x");
    Serial.println(sht4x.readSerial(), HEX);

    // Set precision and heater settings
    sht4x.setPrecision(SHT4X_HIGH_PRECISION);
    sht4x.setHeater(SHT4X_NO_HEATER);
}

void sht4x_update(void) {
    uint32_t current_time = millis();

    // Update measurements periodically
    if (current_time - last_measurement_time >= MEASUREMENT_INTERVAL_MS) {
        sensors_event_t humidity, temp;

        if (sht4x.getEvent(&humidity, &temp)) {
            // Update UI variables
            set_var_temp(temp.temperature);
            set_var_humidity((int32_t)humidity.relative_humidity);

            // Debug output
            Serial.print("Temperature: ");
            Serial.print(temp.temperature);
            Serial.print(" °C, Humidity: ");
            Serial.print(humidity.relative_humidity);
            Serial.println(" %");
        } else {
            Serial.println("SHT4x measurement error");
        }

        last_measurement_time = current_time;
    }
}
