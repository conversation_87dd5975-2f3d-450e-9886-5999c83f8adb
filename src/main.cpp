#include <Arduino.h>
#include "ui/ui.h"
#include "sensors/sht4x.h"
#include "sensors/water_sensor.h"
#include "input/StateButtons.h"
#include "actuators/fan.h"

// Define buttons
PressHoldButton buttonup(1, 1);
PressHoldButton buttondown(2, 1);

void setup() {
    // Initialize serial for debugging
    Serial.begin(115200);
    Serial.println("System starting...");
    
    // Initialize hardware components
    sht4x_init();
    // water_sensor_init();
    fan_init();
    
    // Initialize UI
    ui_init();
    
    Serial.println("System initialized");
    
}

void loop() {
    // Monitor button states
    buttonup.monitorPress();
    buttondown.monitorPress();

    // Update sensor readings
    sht4x_update();
    // water_sensor_update();

    // Update fan control
    if (buttonup.isPressed() || buttonup.isHoldEventRetrig(500)){
        fan_increment_speed(10);
        Serial.println("Button up");
    }
    else if (buttondown.isPressed() || buttondown.isHoldEventRetrig(500)){
        fan_increment_speed(-10);
        Serial.println("Button down");
    }
    fan_update();

    // Update UI
    ui_tick();

    // Small delay to prevent CPU hogging
    delay(10);
}
